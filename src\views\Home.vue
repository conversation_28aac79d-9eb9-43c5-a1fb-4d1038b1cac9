<script setup>
import { ref } from 'vue'
const items = ref([
  {
    label: 'File',
    items: [
      { label: 'New', icon: 'pi pi-plus' },
      { label: 'Open', icon: 't-icon t-icon-plus' },
      { label: 'Test', icon: 't-icon t-icon-assignment-code-filled' },
    ],
  },
])
</script>

<template>
  Hello Home

  <Button icon="pi pi-home" aria-label="Save" />
  <Button label="Profile" icon="pi pi-user" />
  <Button label="Save" icon="pi pi-check" iconPos="right" />
  <Button label="Search" icon="pi pi-search" iconPos="top" />
  <Button label="Update" icon="pi pi-refresh" iconPos="bottom" />
</template>
