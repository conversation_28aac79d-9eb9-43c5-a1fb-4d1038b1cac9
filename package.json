{"name": "kgportal", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"@primeuix/themes": "^1.2.1", "@tailwindcss/vite": "^4.1.11", "ofetch": "^1.4.1", "primeicons": "^7.0.0", "primevue": "^4.3.6", "tailwindcss": "^4.1.11", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@primevue/auto-import-resolver": "^4.3.6", "@vitejs/plugin-vue": "^6.0.0", "prettier": "^3.6.2", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^8.0.0"}}