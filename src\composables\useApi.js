import { ref } from 'vue'

// code 为 0 代表成功，其他均为失败状态
const SUCCESS_CODE = 0

/**
 * 一个通用的、可处理标准API响应结构的 Composable。
 * @param {Function} apiMethod - 一个返回 Promise 的 Service 方法。
 * @param {Object} options - 配置选项。
 * @param {boolean} options.immediate - 是否在创建时立即执行。
 * @param {Function} options.transform - 一个用于转换和提取数据的函数。
 */
export function useApi(apiMethod, options = {}) {
  const { immediate = false, transform = (res) => ('data' in res ? res.date : res) } = options

  const data = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const execute = async (...args) => {
    loading.value = true
    error.value = null
    try {
      // 1. 调用原始 Service 方法，获取完整的后端响应
      const rawResponse = await apiMethod(...args)

      // 2. 检查业务是否成功
      if (rawResponse.code !== SUCCESS_CODE) {
        // 如果 code 不为 0，则抛出错误
        throw new Error(rawResponse.message || '未知错误')
      }

      // 3. 如果成功，使用 transform 函数提取并转换数据（调用者会告诉我们如何从响应中提取真正的业务数据）
      data.value = transform(rawResponse)
    } catch (err) {
      // err 可能是网络错误，也可能是上面抛出的业务错误
      error.value = err
      // 统一提示错误信息
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  if (immediate) {
    execute()
  }

  return {
    data,
    loading,
    error,
    execute,
  }
}
