<script setup>
import { useApi } from '@/composables/useApi'
import { AppService } from '@/service/AppService'
import { onMounted } from 'vue'

const { data: course, loading, error, execute: getCourse } = useApi(AppService.getCourse)
onMounted(() => getCourse('zcAU3YXIMdWPyGd'))
</script>

<template>
  <div class="min-h-full">
    <nav class="bg-blue-800">
      <div class="mx-auto max-w-7xl px-4">
        <div class="flex h-16 items-center justify-between">
          <div class="flex items-center">
            <div class="shrink-0">
              <img src="@/assets/images/neuedu_white.png" class="w-[100px]" />
            </div>
            <div>
              <div class="ml-10 flex items-baseline space-x-4"></div>
            </div>
          </div>
          <div>
            <button
              class="layout-topbar-menu-button layout-topbar-action"
              v-styleclass="{
                selector: '@next',
                enterFromClass: 'hidden',
                enterActiveClass: 'animate-scalein',
                leaveToClass: 'hidden',
                leaveActiveClass: 'animate-fadeout',
                hideOnOutsideClick: true,
              }"
            >
              <i class="pi pi-ellipsis-v"></i>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <main>
      <div class="mx-auto max-w-7xl px-4 py-6">
        <RouterView />
      </div>
    </main>
  </div>
</template>
